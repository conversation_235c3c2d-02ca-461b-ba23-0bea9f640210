import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import {
  Box,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material";

import {
  capitalizeWords,
  downloadFile,
  globalStyles,
  useMutation,
  useQuery,
} from "@/helpers";
import Input from "@/components/input/Input";
import { ButtonOutline, ButtonPrimary, DataTable } from "@/components";
import { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { CrudFilter } from "@refinedev/core";
import { GridColDef } from "@mui/x-data-grid";

const sectionStyle = {
  color: "var(--text-grey)",
  borderRadius: "18px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function EditCarianDokumen() {
  const classes = globalStyles();
  const [openModal, setOpenModal] = useState(false);
  const { t } = useTranslation();
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [GPKISignature, setGPKISignatureId] = useState("");
  const [documentURL, setDocumentURL] = useState("");
  const [documentId, setDocumentId] = useState("");
  const [documentName, setDocumentName] = useState("");
  const { id } = useParams();
  const { reset, setValue, watch, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      otp: "",
    },
  });

  const OTP_COOLDOWN = 120; // seconds
  const isDisabled = secondsLeft > 0;
  const { data, isLoading, refetch } = useQuery({
    url: `society/searchInformation/findPendingById`,
    autoFetch: false,
  });

  const { fetch: fetchOTP, isLoading: isLoadingOTP } = useMutation({
    url: "society/searchInformation/gpki/requestOtp",
    method: "post",
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        setSecondsLeft(OTP_COOLDOWN);
        setGPKISignatureId(data?.data?.data);
      }
    },
  });

  const { fetch: fetchDoc, isLoading: isLoadingDoc } = useMutation({
    url: "society/searchInformation/gpki/signDocument",
    method: "put",
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        setDocumentURL(data?.data?.data?.signedDocumentUrl);
        setGPKISignatureId("");
        const filters: CrudFilter[] = [
          { field: "id", operator: "eq", value: id },
        ];
        refetch({ filters });
      }
    },
  });

  const { fetch: rejectDoc, isLoading: isLoadingReject } = useMutation({
    url: "society/searchInformation/document/updateApprovalStatus",
    method: "put",
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        const filters: CrudFilter[] = [
          { field: "id", operator: "eq", value: id },
        ];
        refetch({ filters });
      }
    },
  });

  useEffect(() => {
    if (secondsLeft === 0) return;

    const interval = setInterval(() => {
      setSecondsLeft((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [secondsLeft]);

  const handleRequestOtp = () => {
    if (isDisabled) return;
    fetchOTP({ searchInformationDocumentId: documentId });
  };

  const handleReject = (id: string) => {
    rejectDoc({ applicationStatusCode: 4, searchInformationDocumentId: id });
  };

  const handleVerifyModal = (id: string, name: string) => {
    setDocumentId(id);
    setDocumentName(name);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setDocumentId("");
    setDocumentName("");
    setDocumentURL("");
    setOpenModal(false);
  };

  useEffect(() => {
    if (id) {
      const filters: CrudFilter[] = [
        { field: "id", operator: "eq", value: id },
      ];
      refetch({ filters });
    }
  }, [id]);

  const columns: GridColDef[] = [
    {
      field: "searchInformationDocumentName",
      headerName: t("organizationalDocuments"),
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;

        return <Box>{row?.searchInformationDocumentName}</Box>;
      },
    },
    {
      field: "action",
      headerName: t("action"),
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;

        return (
          <Box sx={{ display: "flex", gap: 1, justifyContent: "right" }}>
            {Number(data?.data?.data?.applicationStatusCode) === 2 ? (
              <>
                <ButtonOutline
                  onClick={() => {
                    handleReject(row?.searchInformationDocumentId);
                  }}
                  sx={{ borderColor: "var(--error)" }}
                >
                  {isLoadingReject ? <CircularProgress /> : "tolak"}
                </ButtonOutline>
                <ButtonOutline
                  onClick={() =>
                    handleVerifyModal(
                      row?.searchInformationDocumentId,
                      row?.searchInformationDocumentName
                    )
                  }
                >
                  {t("verify")}
                </ButtonOutline>
              </>
            ) : Number(data?.data?.data?.applicationStatusCode) === 3 ? (
              <Typography sx={{ color: "var(--primary-color)" }}>
                {capitalizeWords(t("muatTurun"))}
              </Typography>
            ) : Number(data?.data?.data?.applicationStatusCode) === 4 ? (
              <Typography sx={{ color: "var(--error)" }}>
                {capitalizeWords(t("muatTurun"))}
              </Typography>
            ) : null}
          </Box>
        );
      },
    },
  ];

  const onSubmit = (data: any) => {
    fetchDoc({
      gpkiSignatureId: GPKISignature,
      otp: data?.otp,
      searchInformationDocumentId: documentId,
    });
  };

  const formatTime = (totalSeconds: number) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
  };

  const handleDownload = async () => {
    downloadFile({ data: documentURL, name: documentName });
    const filters: CrudFilter[] = [{ field: "id", operator: "eq", value: id }];
    refetch({ filters });
    return;
  };

  return (
    <Box sx={{ display: "grid", gap: 2, mt: 2 }}>
      <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Input
            label={t("applicationName")}
            name="applicationName"
            value={data?.data?.data?.applicationName ?? "-"}
            disabled
          />
          <Input
            label={t("organizationName")}
            name="societyName"
            value={data?.data?.data?.societyName ?? "-"}
            disabled
          />
        </Box>
      </Box>

      <Box className={classes.section} sx={{ display: "grid", gap: 2 }}>
        <Box
          sx={{
            textAlign: "center",
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            py: 2,
          }}
        >
          <Typography variant="h5" gutterBottom>
            {isLoading ? (
              <CircularProgress sx={{ color: "white" }} />
            ) : data?.data?.data?.searchInformationDocuments?.length ? (
              data?.data?.data?.searchInformationDocuments?.length
            ) : (
              0
            )}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
            {t("document")}
          </Typography>
        </Box>

        <Box className={classes.sectionBox}>
          <Typography className="title" sx={sectionStyle} mb={3}>
            {t("permohonanCarianDokumen")}
          </Typography>
          <DataTable
            columns={columns as any}
            rows={
              data?.data?.data?.searchInformationDocuments
                ? data?.data?.data?.searchInformationDocuments
                : []
            }
            totalCount={
              data?.data?.data?.searchInformationDocuments?.length
                ? data?.data?.data?.searchInformationDocuments?.length
                : 0
            }
            page={page}
            rowsPerPage={pageSize}
            onPageChange={(newPage) => setPage(newPage)}
            onPageSizeChange={(newPageSize) => {
              setPage(1);
              setPageSize(newPageSize);
            }}
            clientPaginationMode
            isLoading={isLoading || isLoadingReject || isLoadingDoc}
          />
        </Box>
      </Box>
      <Dialog
        open={openModal}
        onClose={() => handleCloseModal()}
        PaperProps={{
          style: {
            borderRadius: "8px",
            padding: "9px",
            width: "100%",
          },
        }}
      >
        <DialogTitle sx={{ textAlign: "center", color: "var(--text-grey)" }}>
          {t("verificationOfDigitalDocuments")}
        </DialogTitle>
        <DialogContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box className={classes.section}>
              <Box
                className={classes.sectionBox}
                sx={{
                  display: "grid",
                  justifyItems: "center",
                  alignItems: "center",
                  gap: 3,
                }}
              >
                <Input
                  label={t("enterOtp")}
                  name="otp"
                  value={watch("otp") || ""}
                  disabled={!GPKISignature}
                  onChange={(e) => setValue("otp", e.target.value)}
                />

                <Box
                  sx={{
                    display: "flex",
                    justifyItems: "space-between",
                    width: "100%",
                  }}
                  gap={1}
                >
                  <ButtonOutline
                    disabled={
                      isDisabled || isLoading || isLoadingReject || isLoadingDoc
                    }
                    onClick={() => handleRequestOtp()}
                    fullWidth
                  >
                    {isLoadingOTP ? (
                      <CircularProgress />
                    ) : isDisabled ? (
                      `${t("requestOTP")} (${formatTime(secondsLeft)})`
                    ) : (
                      t("requestOTP")
                    )}
                  </ButtonOutline>
                  <ButtonPrimary
                    fullWidth
                    disabled={
                      !GPKISignature ||
                      isLoading ||
                      isLoadingReject ||
                      isLoadingDoc
                    }
                    type="submit"
                  >
                    {isLoadingDoc ? <CircularProgress /> : t("validateDoc")}
                  </ButtonPrimary>
                </Box>
              </Box>
            </Box>
          </form>
        </DialogContent>
        <DialogActions sx={{ justifyContent: "center", gap: 2 }}>
          <ButtonPrimary
            onClick={() => handleDownload()}
            disabled={
              !documentURL || isLoading || isLoadingReject || isLoadingDoc
            }
          >
            {t("downloadDocument")}
          </ButtonPrimary>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default EditCarianDokumen;
