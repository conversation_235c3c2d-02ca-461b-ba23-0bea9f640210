import { Box, Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import { getMalaysiaAddressList } from "@/helpers/utils";
import { useNavigate } from "react-router-dom";
import {
  ApplicationStatusEnum,
  NEW_PermissionNames,
  NewSocietyBranchStatus,
  pageAccessEnum,
} from "@/helpers/enums";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import { ButtonPrimary } from "@/components/button";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import { useMutation, useQuery } from "@/helpers";
import DataTable, { IColumn } from "@/components/datatable";
import { CrudFilter } from "@refinedev/core";
import Input from "@/components/input/Input";
import ForbiddenPage from "@/pages/forbidden";
import { MaklumatTabProps } from "../maklumatSelectionTabs";
import AuthHelper from "@/helpers/authHelper";
import ConfirmationDialog from "@/components/dialog/confirm";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function PenyataTahunanTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const navigate = useNavigate();
    const hasDeletePermission = AuthHelper.hasPageAccess(
      NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children.INDUK
        .children.PENYATA_TAHUNAN_INDUK.label,
      pageAccessEnum.Delete
    );
    const [selectedId, setSelectedId] = useState<string | number | null>(null);
    const [selectedSocietyId, setSelectedSocietyId] = useState<
      string | number | null
    >(null);
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
    const { control, setValue, watch, getValues, reset, handleSubmit } =
      useForm<FieldValues>({
        defaultValues: {
          pageNo: 1,
          pageSize: 10,
          searchQuery: "",
          applicationStatusCode: "",
          stateCode: "",
          statementYear: "",
        },
      });

    const [total, setTotal] = useState<number>(0);
    const [statementList, setStatementList] = useState([]);

    const malaysiaAddressList = getMalaysiaAddressList() ?? [];
    const stateOptions = useMemo(() => {
      return malaysiaAddressList.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }, []);

    const getStateName = (stateCode: any) => {
      const stateName = malaysiaAddressList.filter(
        (i: any) => i.id == stateCode
      );
      return stateName[0]?.name ? stateName[0]?.name : "-";
    };

    const statusPermohonan = Object.entries(ApplicationStatusEnum)
      .filter(([key, value]) => key === "1" || key === "17")
      .map(([key, value]) => ({
        value: key,
        label: t(value),
      }));

    const handleClearSearch = () => {
      reset();
      refetchStatementList();
    };

    const { fetch: deleteStatement, isLoading: isDeletingStatement } =
      useMutation({
        url: `society/statement/${selectedId}/delete`,
        method: "delete",
        onSuccess: (data) => {
          refetchStatementList();
          setSelectedId(null);
          setSelectedSocietyId(null);
          setOpenConfirmDialog(false);
        },
      });

    const handleConfirmDelete = () => {
      if (selectedId && selectedSocietyId) {
        deleteStatement({ societyId: selectedSocietyId });
      }
    };

    const handleDeleteClick = (
      id: string | number,
      societyId: string | number
    ) => {
      setSelectedId(id);
      setSelectedSocietyId(societyId);
      setOpenConfirmDialog(true);
    };

    const handleSearch = () => {
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: getValues("pageSize") },
        { field: "pageNo", operator: "eq", value: getValues("pageNo") },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
        {
          field: "statementYear",
          operator: "eq",
          value: getValues("statementYear"),
        },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: getValues("applicationStatusCode"),
        },
        { field: "stateCode", operator: "eq", value: getValues("stateCode") },
      ];
      refetchStatementList({ filters });
    };

    const active = [NewSocietyBranchStatus.AKTIF_1];

    const columns: IColumn[] = [
      {
        field: "societyName",
        headerName: t("namaPertubuhan"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.societyName}</Box>;
        },
      },
      {
        field: "societyNo",
        headerName: t("noPertubuhan"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.societyNo ? row?.societyNo : "-"}</Box>;
        },
      },
      {
        field: "statementApplicationStatusCode",
        headerName: t("statusTahunPenyata"),
        align: "center",
        renderCell: ({ row }: any) => {
          return (
            <Box>
              {t(
                ApplicationStatusEnum[
                  (row?.statementApplicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                    "0"
                ]
              )}
            </Box>
          );
        },
      },
      {
        field: "statementYear",
        headerName: t("tahunPenyata"),
        align: "center",
        headerAlign: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.statementYear}</Box>;
        },
      },
      {
        field: "submissionDate",
        headerName: t("submissionDate"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.submissionDate ? row?.submissionDate : "-"}</Box>;
        },
      },
      {
        field: "societyStateCode",
        headerName: t("state"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{getStateName(row?.societyStateCode)}</Box>;
        },
      },
      {
        field: "statusPertubuhan",
        headerName: t("organizationStatus"),
        align: "center",
        renderCell: ({ row }: any) => {
          const isActive = active.includes(row?.societyStatusCode);
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Typography
                className="status-pertubuhan-text"
                sx={{
                  backgroundColor: "#fff",
                  border: `2px solid ${
                    isActive ? "var(--success)" : "var(--error)"
                  }`,
                }}
              >
                {isActive ? t("active") : t("inactive")}
              </Typography>
            </Box>
          );
        },
      },
      {
        field: "actions",
        headerName: "",
        align: "right",
        renderCell: ({ row }: any) => {
          return (
            <Box
              sx={{
                fontWeight: "500",
                textAlign: "center",
                display: "flex",
              }}
            >
              <IconButton
                color="primary"
                onClick={() => goDetail(row, false)}
                sx={{ width: "2rem", height: "2rem" }}
              >
                <EditIcon sx={{ width: "1rem", height: "1rem" }} />
              </IconButton>
              <IconButton
                onClick={() => goDetail(row, true)}
                sx={{ width: "2rem", height: "2rem" }}
              >
                <EyeIcon sx={{ width: "1rem", height: "1rem" }} />
              </IconButton>
              {hasDeletePermission ? (
                <IconButton
                  sx={{
                    width: "2rem",
                    height: "2rem",
                    color: "#FF0000",
                  }}
                  onClick={() =>
                    handleDeleteClick(row?.statementId, row?.societyId)
                  }
                >
                  <TrashIcon color="red" />
                </IconButton>
              ) : null}
            </Box>
          );
        },
      },
    ];

    const goDetail = (data: any, isView: boolean) => {
      navigate("pertubuhan/penyata-tahunan", {
        state: { data: data, isView: isView },
      });
    };

    const handleChangePage = (newPage: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: watch("pageSize"), operator: "eq" },
        { field: "pageNo", value: newPage, operator: "eq" },
      ];
      setValue("pageNo", newPage);
      refetchStatementList({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: newPageSize, operator: "eq" },
        { field: "pageNo", value: watch("pageNo"), operator: "eq" },
      ];
      setValue("pageSize", newPageSize);
      refetchStatementList({ filters });
    };

    const {
      data: statementListData,
      isLoading: statementListIsLoading,
      refetch: refetchStatementList,
    } = useQuery({
      url: `society/statement/internal/getStatement`,
      filters: [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
      ],
      autoFetch: false,
      onSuccess: (data) => {
        const statementData = data?.data?.data?.data || [];
        const total = data?.data?.data?.total;
        setTotal(total);
        setStatementList(statementData);
      },
    });

    useEffect(() => {
      const filters: CrudFilter[] = [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
      ];
      refetchStatementList({ filters });
    }, []);

    return (
      <>
        <form onSubmit={handleSubmit(handleSearch)}>
          <Box>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
            >
              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                }}
              >
                <Typography sx={sectionStyle}>
                  {t("senaraiPertubuhan")}
                </Typography>
                <Box>
                  {/* state */}
                  <Controller
                    name="stateCode"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="select"
                        label={t("state")}
                        options={stateOptions}
                      />
                    )}
                  />
                  {/* status permohonan */}
                  <Controller
                    name="applicationStatusCode"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="select"
                        label={t("statusPermohonan")}
                        options={statusPermohonan}
                      />
                    )}
                  />
                  {/* tahun penyata */}
                  <Controller
                    name="statementYear"
                    control={control}
                    rules={{
                      pattern: {
                        value: /^\d{4}$/,
                        message: "Enter a valid 4-digit year",
                      },
                    }}
                    render={({ field }) => (
                      <Input
                        {...field}
                        inputProps={{
                          maxLength: 4,
                          inputMode: "numeric",
                        }}
                        // isLabel={false}
                        label={t("tahunPenyata")}
                        onChange={(e) => {
                          const onlyDigits = e.target.value.replace(/\D/g, "");
                          field.onChange(onlyDigits);
                        }}
                      />
                    )}
                  />
                  {/* search */}
                  <Controller
                    name="searchQuery"
                    control={control}
                    render={({ field }) => (
                      <Input {...field} label={t("search")} />
                    )}
                  />
                </Box>
                <Grid container mt={1} spacing={2}>
                  <Grid
                    item
                    xs={12}
                    sx={{
                      display: "flex",
                      justifyContent: "flex-end",
                      gap: 1,
                    }}
                  >
                    <ButtonPrevious onClick={handleClearSearch}>
                      {t("previous")}
                    </ButtonPrevious>
                    <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
                  </Grid>
                </Grid>
              </Box>
            </Box>
            {/* ============= */}
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
            >
              <Box
                sx={{
                  textAlign: "center",
                  color: "#fff",
                  borderRadius: "13px",
                  backgroundColor: "var(--primary-color)",
                  py: 2,
                }}
              >
                <Typography variant="h5" gutterBottom>
                  {total}
                </Typography>
                <Typography
                  variant="body1"
                  sx={{ fontWeight: "500 !important" }}
                >
                  {t("rekodDijumpai")}
                </Typography>
              </Box>

              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mt: 2,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {t("")}
                </Typography>
                <DataTable
                  columns={columns}
                  rows={statementList}
                  page={watch("pageNo")}
                  rowsPerPage={watch("pageSize")}
                  totalCount={total}
                  onPageChange={handleChangePage}
                  onPageSizeChange={handleChangePageSize}
                  isLoading={statementListIsLoading}
                  customNoDataText={t("noRecordForStatus")}
                />
              </Box>
            </Box>
          </Box>
        </form>
        <ConfirmationDialog
          open={openConfirmDialog}
          onClose={() => setOpenConfirmDialog(false)}
          isMutation={isDeletingStatement}
          onConfirm={handleConfirmDelete}
          onCancel={() => setOpenConfirmDialog(false)}
          title={t("confirmDelete")}
          message={t("confirmDeletePenyata")}
        />
      </>
    );
  }
}
export default PenyataTahunanTab;
