import { ButtonOutline, DataTable } from "@/components";
import { EditIcon } from "@/components/icons";
import Input from "@/components/input/Input";
import { getStateNameById, globalStyles, useQuery } from "@/helpers";
import { Box, CircularProgress, IconButton, Typography } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

const sectionStyle = {
  color: "var(--text-grey)",
  borderRadius: "18px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function CarianDokumen() {
  const navigate = useNavigate();
  const classes = globalStyles();

  const { t } = useTranslation();
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const { reset, setValue, watch, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      pageNo: 1,
      pageSize: 10,
      searchQuery: "",
      applicantName: "",
    },
  });

  const { data, isLoading, refetch } = useQuery({
    url: "society/roDecision/getAllPending/searchDocument",
    filters: [
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
      {
        field: "searchQuery",
        value: watch("searchQuery"),
        operator: "eq",
      },
      {
        field: "applicantName",
        value: watch("applicantName"),
        operator: "eq",
      },
    ],
    autoFetch: true,
  });

  const onSubmit = (data: any) => {
    refetch();
  };

  const resetValues = () => {
    reset();
    setPage(1);
    setPageSize(10);
  };

  const columns: GridColDef[] = [
    {
      field: "applicationName",
      headerName: t("applicationName"),
      align: "left",
      headerAlign: "left",
      renderCell: (params: any) => {
        const row = params?.row;

        return <Box>{row?.applicationName ? row?.applicationName : "-"}</Box>;
      },
    },
    {
      field: "organizationalDocuments",
      headerName: t("organizationalDocuments"),
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;

        return <Box>{row?.organizationalDocuments}</Box>;
      },
    },
    {
      field: "state",
      headerName: t("state"),
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box sx={{ textAlign: "center" }}>
            {getStateNameById(row?.society?.stateCode)}
          </Box>
        );
      },
    },

    {
      field: "action",
      headerName: t("action"),
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;

        return (
          <Box>
            <IconButton
              sx={{ width: "3rem", height: "3rem" }}
              // disabled={inquiryValuesIsNullPartial}
              onClick={() => navigate(`edit/${row?.id}`)}
            >
              <EditIcon />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  return (
    <Box sx={{ display: "grid", gap: 3, mt: 2 }}>
      {/* main content */}
      <Box sx={{ flex: 1 }}>
        <form
          style={{ marginBottom: "20px" }}
          onSubmit={handleSubmit(onSubmit)}
        >
          <Box className={classes.section}>
            <Box className={classes.sectionBox}>
              <Typography className="title" mb={3}>
                {t("search")}
              </Typography>
              <Input
                label={t("search")}
                name="searchQuery"
                value={watch("searchQuery") || ""}
                onChange={(e) => setValue("searchQuery", e.target.value)}
              />
              <Input
                label={t("applicationName")}
                name="applicationName"
                value={watch("applicationName") || ""}
                onChange={(e) => setValue("applicationName", e.target.value)}
              />

              <Box sx={{ display: "flex", justifyContent: "flex-end" }} gap={1}>
                <ButtonOutline onClick={() => resetValues()}>
                  {t("semula")}
                </ButtonOutline>
              </Box>
            </Box>
          </Box>
        </form>

        <Box className={classes.section} sx={{ display: "grid", gap: 2 }}>
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {isLoading ? (
                <CircularProgress sx={{ color: "white" }} />
              ) : data?.data?.data?.total ? (
                data?.data?.data?.total
              ) : (
                0
              )}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("associationAwaitingApproval")}
            </Typography>
          </Box>

          <Box className={classes.sectionBox}>
            <Typography className="title" sx={sectionStyle} mb={3}>
              {t("permohonanCarianDokumen")}
            </Typography>
            <DataTable
              columns={columns as any}
              rows={data?.data?.data?.data}
              page={page}
              rowsPerPage={pageSize}
              totalCount={data?.data?.data?.total}
              onPageChange={(newPage) => setPage(newPage)}
              onPageSizeChange={(newPageSize) => {
                setPage(1);
                setPageSize(newPageSize);
              }}
              isLoading={isLoading}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

export default CarianDokumen;
