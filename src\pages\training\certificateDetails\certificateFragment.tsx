import React, { useState } from "react";
import { Box, Grid, LinearProgress, Typography } from "@mui/material";
import { ButtonOutline, ButtonPrimary, DialogConfirmation } from "@/components";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { TrainingRequiredIcon } from "@/components/icons/trainingRequired";
import { ViewCertIcon } from "@/components/icons/viewCert";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";

interface CertificateFragmentProps {
  item: any;
  width?: string;
}

const CertificateFragment: React.FC<CertificateFragmentProps> = ({ item }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [openModal, setOpenModal] = useState(false);

  console.log("item", item);
  const checkAnswers = () => {
    navigate("/latihan/info", { state: { enroll: item, answerScheme: true } });
  };

  const { mutate: enroll, isLoading: isLoadingEnroll } = useCustomMutation();
  const Enroll = (): void => {
    enroll(
      {
        url: `${API_URL}/society/training/courses/enroll/${item.id}/retake`,
        method: "post",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            const enroll = data?.data?.data;
            let msg = "";
            if (enroll.completionStatus === "COMPLETED") {
              msg = t("TRAINING_SUCCESS");
            } else if (enroll.completionStatus === "IN_PROGRESS") {
              msg = data?.data?.msg.includes("Successfully")
                ? t("TRAINING_ENROLL_SUCCESSFUL")
                : t("TRAINING_ENROLLED");
              navigate("/latihan/info", { state: { enroll: enroll } });
            }
            return {
              message: msg || data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  console.log("item", item);

  return (
    <>
      <Box sx={{ width: "100%" }}>
        {/* TOP CARD */}
        <Box
          sx={{
            p: 2,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mt: 1,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontWeight: 400,
              fontSize: 12,
            }}
          >
            {item.title}
          </Typography>

          <Typography
            sx={{
              color: "#666666",
              fontWeight: 500,
              fontSize: 14,
            }}
          >
            {item.description}
          </Typography>

          {/* STATUS */}
          <Box sx={{ display: "flex", width: "25%", mt: 2 }}>
            <Box
              sx={{
                border:
                  item.completionStatus === "COMPLETED"
                    ? "1px solid #2E7CF6"
                    : "1px solid #FF0000",
                backgroundColor:
                  item.completionStatus === "COMPLETED"
                    ? "#2E7CF64D"
                    : "#FF00004D",
                borderRadius: 5,
                flex: 1,
                py: 1,
                mb: 2,
              }}
            >
              <Typography
                sx={{
                  color: "#fff",
                  fontWeight: 500,
                  fontSize: 10,
                  textAlign: "center",
                  lineHeight: "100%",
                }}
              >
                {item.completionStatus === "COMPLETED"
                  ? t("LULUS")
                  : t("GAGAL")}
              </Typography>
            </Box>
          </Box>

          {/* POSTER */}
          <Box
            sx={{
              height: 200,
              backgroundImage: `url(${item.poster ?? "/latihanSample/images5.jpg"})`,
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center",
              display: "flex",
              alignItems: "flex-start",
              px: 1,
              py: 1,
              borderRadius: 2.5,
            }}
          >
            <Box
              sx={{
                height: 32,
                borderRadius: 1.5,
                backgroundColor: "#fff",
                display: "flex",
                alignItems: "center",
                gap: 1,
                p: 1,
              }}
            >
              <TrainingRequiredIcon />
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: 400,
                  fontSize: 10,
                }}
              >
                {item.required ? t("compulsoryCourse") : ""}
              </Typography>
            </Box>
          </Box>

          {/* PROGRESS */}
          <Box sx={{ mt: 2, display: "flex", alignItems: "center" }}>
            <LinearProgress
              variant="determinate"
              value={(item.currentStep / item.totalStep) * 100}
              sx={{
                flex: 1,
                height: 15,
                borderRadius: 3,
                backgroundColor: "#E0E0E0",
                "& .MuiLinearProgress-bar": {
                  backgroundColor: "#00BCD4",
                  borderRadius: 3,
                },
              }}
            />
            <Typography
              sx={{
                ml: 5,
                color: "#666666",
                fontWeight: 400,
                fontSize: 16,
                lineHeight: "18px",
              }}
            >
              {`${((item.currentStep / item.totalStep) * 100).toFixed(0)} %`}
            </Typography>
          </Box>
        </Box>

        {/* BOTTOM ACTIONS */}
        <Grid container spacing={1} sx={{ mt: 0.1 }}>
          <Grid item xs={2}>
            <Box
              sx={{
                p: 0.5,
                border: "1px solid #D9D9D9",
                borderRadius: "10px",
                height: 50,
              }}
            >
              <Box
                sx={{
                  p: 1,
                  backgroundColor:
                    item.completionStatus === "COMPLETED"
                      ? "#0CA6A6"
                      : "#D9D9D9",
                  cursor:
                    item.completionStatus === "COMPLETED"
                      ? "pointer"
                      : "default",
                  width: "100%",
                  height: "100%",
                  borderRadius: "5px",
                }}
                onClick={() => {
                  if (item.completionStatus === "COMPLETED") {
                    navigate("sijil", { state: { enrollmentData: item } });
                  }
                }}
              >
                <ViewCertIcon sx={{ width: "100%", height: "100%" }} />
              </Box>
            </Box>
          </Grid>

          <Grid item xs={10}>
            <Box
              sx={{
                p: 0.5,
                border: "1px solid #D9D9D9",
                borderRadius: "10px",
                height: 50,
                display: "flex",
                gap: 1,
                alignItems: "center",
              }}
            >
              <ButtonPrimary
                sx={{
                  flex: 1,
                  minWidth: 0,
                  minHeight: 40,
                  fontWeight: 400,
                  fontSize: 12,
                  p: 0,
                }}
                disabled={item.completionStatus === "COMPLETED"}
                onClick={() => setOpenModal(true)}
              >
                {t("retakeQuiz")}
              </ButtonPrimary>

              <ButtonOutline
                sx={{
                  flex: 1,
                  minWidth: 0,
                  minHeight: 40,
                  fontWeight: 400,
                  fontSize: 12,
                  p: 0,
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                }}
                onClick={checkAnswers}
              >
                {t("answerScheme")}
              </ButtonOutline>
            </Box>
          </Grid>
        </Grid>
      </Box>

      <DialogConfirmation
        open={openModal}
        onClose={() => {
          setOpenModal(false);
        }}
        onAction={Enroll}
        isMutating={false}
        onConfirmationText={t("CONFIRM_RETAKE_QUIZ")}
      />
    </>
  );
};

export default CertificateFragment;
