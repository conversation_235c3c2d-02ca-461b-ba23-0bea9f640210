import { <PERSON>, Grid, Icon<PERSON>utton, Switch, Typography } from "@mui/material";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import TakwimInput from "@/components/input/TakwimInput";
import TakwimTextField from "@/components/input/TakwimTextField";
import TakwimSelect from "@/components/input/TakwimSelect";
import { ButtonPrimary } from "@/components";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";
import { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import TextareaWithLabel from "@/components/input/TextareaWithLabel";
import { LogoLarangan } from "@/types/larangan/logoLarangan";
import dayjs from "dayjs";
import { ApiResponse, laranganService } from "@/services/laranganService";
import { useNavigate } from "react-router-dom";
import { DocumentUploadType, useUploadPresignedUrl } from "@/helpers";
import { useNotification } from "@refinedev/core";
import { LoadingOverlay } from "@/components/loading";
import { useLaranganContext } from "@/contexts/laranganProvider";

interface TabLogoTerlarangData {
  id: number | string | null;
  readAccess: boolean;
  createAccess: boolean;
  updateAccess: boolean;
  deleteAccess: boolean;
  mode: string | null;
  isEditMode: boolean;
  isDisabled: boolean;
  tab: string | null;
  tabOptions: { label: string; value: number }[];
  isDisableImage: boolean;
}

interface UploadResponse {
  data: {
    data: {
      url: string;
    };
  };
}
export const TabLogoTerlarang = ({
  id,
  readAccess,
  createAccess,
  updateAccess,
  deleteAccess,
  mode,
  isEditMode,
  isDisabled,
  tab,
  tabOptions,
  isDisableImage,
}: TabLogoTerlarangData) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [logoPreview, setLogoPreview] = useState("");
  const [selectedLogoFile, setSelectedLogoFile] = useState<File | null>(null);
  const {isViewMode, setIsViewMode} = useLaranganContext();
  const [errors, setErrors] = useState<any>({
    logo: "",
    remarks: "",
    activeRemarks: "",
  });
  const [currentDateTime, setCurrentDateTime] = useState(
    dayjs().format("DD-MM-YYYY HH:mm:ss")
  );
  const navigate = useNavigate();
  const [customDialogMessage, setCustomDialogMessage] = useState<
    string | null | undefined
  >(null);
  const [logoLaranganFormData, setLogoLaranganFormData] = useState<
    Partial<LogoLarangan>
  >({});
  const { open: openNotification } = useNotification();

  const handleConfirm = async () => {
    await handleSave();
  };

  const validateForm = () => {
    const valid = true;
    const errors: { [key: string]: string } = {};
    if (!selectedLogoFile && !logoLaranganFormData.logoUrl) {
      errors.logo = "Sila muat naik logo";
    }
    if (!logoLaranganFormData.remarks) {
      errors.remarks = "Sila isi catatan";
    }

    setErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    const logoLarangan: Partial<LogoLarangan> = {
      activeRemarks: logoLaranganFormData.activeRemarks,
      remarks: logoLaranganFormData.remarks,
      createdDate: currentDateTime,
      active: logoLaranganFormData.active ?? false,
      logoUrl: logoLaranganFormData.logoUrl,
    };

    if (!validateForm()) {
      setOpenDialog(false);
      openNotification?.({
        type: "error",
        message: "Sila lengkapkan maklumat",
        description: "Sila lengkapkan maklumat",
      });
      return;
    }

    // return;
    if (isEditMode) {
      try {
        const res: ApiResponse<any> = await laranganService.updateLaranganLogo(
          logoLarangan,
          id
        );
        if (res.code === 200) {
          setCustomDialogMessage(res?.msg);
          navigate(
            `/penguatkuasaan/daftar_nama_larangan/senarai_logo_larangan`
          );
        } else {
          throw new Error(res.msg || "Failed to update larangan logo");
        }
      } catch {
        setCustomDialogMessage("Ralat semasa mengemaskini data");
      }
    } else {
      // create new data
      try {
        // call Api
        handleLogoChange;
        const res: ApiResponse<any> = await laranganService.createLaranganLogo(
          logoLarangan
        );
        if (res.code === 201) {
          setCustomDialogMessage(res?.msg);
          navigate(
            `/penguatkuasaan/daftar_nama_larangan/senarai_logo_larangan?`
          );
        } else {
          throw new Error(res.msg || "Failed to create larangan");
        }
      } catch (error) {
        const message =
          error instanceof Error ? error.message : "Something went wrong";
        setCustomDialogMessage(message);
      }
    }
  };

  useEffect(() => {
    // Fetch data based on id
    if (!id) {
      const interval = setInterval(() => {
        setCurrentDateTime(dayjs().format("DD-MM-YYYY HH:mm:ss"));
      }, 1000);

      return () => clearInterval(interval);
    }
  }, []);
  useEffect(() => {
    if (id && tab == tabOptions[1].label) {
      fetchLogoLaranganById(id);

    }
  }, [isEditMode]);

  const { upload: uploadLogo } = useUploadPresignedUrl({
    onSuccessUpload: (data: any) => {
      // Handle successful upload
      if (data.data.data.url) {
        setLogoLaranganFormData((prev) => ({
          ...prev,
          logoUrl: data.data.data.url,
        }));
        // return data;
        // setBannerUrlS3(data.data.data.url);
      }
    },
  });

  const handleLogoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (file.size > 3 * 1024 * 1024) {
      openNotification?.({
        type: "error",
        message: "Error",
        description: "File size should not exceed 25MB",
      });
      // alert("File size should not exceed 25MB");
      return;
    }

    //check file type
    if (file.type !== "image/png" && file.type !== "image/jpeg") {
      openNotification?.({
        type: "error",
        message: "Error",
        description: "File type not supported",
      });
      return;
    }
    setSelectedLogoFile(file);
    const reader = new FileReader();
    reader.onload = () => {
      setLogoPreview(reader.result as string);
    };
    reader.readAsDataURL(file);
    setIsLoadingData(true);
    await uploadLogo({
      params: {
        type: DocumentUploadType.LOGO_LARANGAN,
        name: file.name,
        societyId: null,
        societyNo: null,
        branchId: null,
        branchNo: null,
        meetingId: null,
        societyCommitteeId: null,
        icNo: "",
      },
      file,
    }).finally(() => {
      setIsLoadingData(false);
    });
  };

  const handleLogoUpload = async () => {
    if (selectedLogoFile) {
      // uploadLogo(logoPreview);
      try {
        await uploadLogo({
          params: {
            type: DocumentUploadType.LOGO_LARANGAN,
            name: selectedLogoFile.name,
            societyId: null,
            societyNo: null,
            branchId: null,
            branchNo: null,
            meetingId: null,
            societyCommitteeId: null,
            icNo: "",
          },
          file: selectedLogoFile,
        });
      } catch (error) {
        console.error("Error uploading banner:", error);
        openNotification?.({
          type: "error",
          message: "Error",
          description: "Failed to upload banner image",
        });
      }
    }
  };

  const fetchLogoLaranganById = async (id: number | string) => {
    try {
      setIsLoadingData(true);
      const response = await laranganService.getLogoLaranganById(id);
      if (response.code === 404) {
        navigate("/penguatkuasaan/daftar_nama_larangan/senarai_logo_larangan");
      }
      setLogoLaranganFormData(response?.data);
      setLogoPreview(response?.data?.logoUrl);
      setCurrentDateTime(
        dayjs(response?.data?.createdDate).format("DD-MM-YYYY HH:mm")
      );
    } catch (error) {
      console.log(error, "ERROR");
    } finally {
      setIsLoadingData(false);
    }
  };

  function handleFormChange<K extends keyof typeof logoLaranganFormData>(
    key: K,
    value: (typeof logoLaranganFormData)[K]
  ) {
    setLogoLaranganFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  }

  return (
    <>
      <LaranganPaper>
        {isLoadingData && <LoadingOverlay />}
        <LaranganBox
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box>
            <Typography
              sx={{
                color: "primary.main",
              }}
            >
              Aktivasi Logo Terlarang
            </Typography>
          </Box>
          <Box>
            <Switch
              checked={logoLaranganFormData.active}
              onChange={(e) => handleFormChange("status", e.target.checked)}
              disabled={isDisabled}
            />
          </Box>
        </LaranganBox>

        <LaranganBox>
          <Typography
            sx={{
              color: "primary.main",
            }}
          >
            Butiran
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 2,
            }}
          >
            {/* IMAGE SECTION */}
            <Grid container spacing={2} alignItems={"flex-start"}>
              <Grid item xs={12} sm={3}>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontSize: "14px",
                    mt: 1,
                  }}
                >
                  Gambar Larangan
                  {true && <span style={{ color: "red" }}>*</span>}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={9}>
                <Box
                  sx={{
                    border: errors.logo ? "1px solid red" : "1px solid #ccc",
                    borderRadius: 1,
                    p: 3,
                    textAlign: "center",
                    cursor: "pointer",
                    position: "relative",
                    minHeight: "200px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    width: "100%",
                    backgroundColor: isDisabled ? "#F5F5F5" : "none",
                  }}
                  onClick={() =>
                    document.getElementById("posterUpload")?.click()
                  }
                >
                  {/* <TakwimInput label="Kata Kunci" value="" onChange={() => {}} /> */}
                  <input
                    type="file"
                    id="posterUpload"
                    hidden
                    accept="image/png, image/jpeg"
                    disabled={isDisableImage}
                    onChange={handleLogoChange}
                  />
                  {logoPreview ? (
                    <Box
                      sx={{
                        width: "100%",
                        height: "100%",
                        position: "relative",
                      }}
                    >
                      <img
                        src={logoPreview}
                        alt="Poster preview"
                        style={{
                          maxWidth: "100%",
                          maxHeight: "200px",
                          objectFit: "contain",
                        }}
                      />
                      <IconButton
                        disabled={isDisableImage}
                        sx={{
                          position: "absolute",
                          top: -10,
                          right: -10,
                          bgcolor: "white",
                          "&:hover": { bgcolor: "#f5f5f5" },
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setLogoPreview("");
                          setLogoLaranganFormData((prev) => ({
                            ...prev,
                            logoUrl: "",
                          }));
                          // setBannerDeleted(true);
                        }}
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  ) : (
                    <>
                      <CloudUploadIcon
                        sx={{ fontSize: 40, color: isDisableImage ? "#5f6867ff" : "#4DB6AC", mb: 1 }}
                      />
                      <Typography variant="body2" color="textSecondary">
                        Muat naik
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                         PNG or JPG  (max. 800x400px)
                      </Typography>
                    </>
                  )}
                </Box>
              </Grid>
            </Grid>
            <TextareaWithLabel
              label="Catatan"
              value={logoLaranganFormData.remarks}
              onChange={(e) => {
                handleFormChange("remarks", e.target.value);
              }}
              disabled={isDisabled}
              error={!!errors.remarks}
              required
            />
            <TakwimInput
              label="Tarikh dan Masa Terbit"
              value={currentDateTime}
              // onChange={() => {}}
              disabled
            />
          </Box>
        </LaranganBox>
        <LaranganBox>
          <Typography
            sx={{
              color: "primary.main",
            }}
          >
            Catatan Status
          </Typography>
          <TextareaWithLabel
            label="Catatan Status"
            value={logoLaranganFormData.activeRemarks}
            onChange={(e) => {
              handleFormChange("activeRemarks", e.target.value);
            }}
            disabled={isDisabled}
          />
        </LaranganBox>
        <Box sx={{ p: "15px", textAlign: "right" }}>
          {!isDisabled ? (
            <ButtonPrimary
              sx={{
                fontWeight: 300,
              }}
              onClick={() => setOpenDialog(true)}
            >
              Kemaskini
            </ButtonPrimary>
          ): <ButtonPrimary
              sx={{
                fontWeight: 300,
              }}
              onClick={() => setIsViewMode(false)}
            >
              Kembali
            </ButtonPrimary>}
        </Box>
      </LaranganPaper>
      <DialogActionFlow
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setCustomDialogMessage(null);
        }}
        onConfirm={async () => {
          await handleConfirm();
        }}
        hideOnError={false}
        confirmationText={
          "Adakah anda pasti untuk menyimpan logo larangan ini?"
        }
        successMessage={
          customDialogMessage
            ? customDialogMessage
            : "Logo larangan berjaya disimpan"
        }
      />
    </>
  );
};
