import React from "react";
import { Box, Button } from "@mui/material";
import { useNavigate, useLocation, Outlet, redirect } from "react-router-dom";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import ForbiddenPage from "@/pages/forbidden";

interface Tab {
  label: string;
  path: string;
  permissionName?: keyof typeof PERMISSIONS;
}

const tabs: Tab[] = [
  {
    label: "Keputusan Induk",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk",
    permissionName: "induk",
  },
  {
    label: "Keputusan Cawangan",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan",
    permissionName: "branch",
  },
  {
    label: "Rayuan",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/rayuan",
    permissionName: "appeal",
  },
  ...(import.meta.env.VITE_APP_ENV === "production"
    ? [
        {
          label: "Senarai Hitam",
          path: "/pengurus-pertubuhan/keputusan-pertubuhan/senarai-hitam",
        },
      ]
    : [
        {
          label: "Carian Dokumen",
          path: "/pengurus-pertubuhan/keputusan-pertubuhan/carian-dokumen",
        },
      ]),
  {
    label: "Kuiri",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri",
    permissionName: "kuiri",
  },
];

const PERMISSIONS = {
  induk:
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.label,
  branch:
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_CAWANGAN.label,
  appeal: `${NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children.RAYUAN.label}:${pageAccessEnum.Read}`,
  kuiri:
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children.KUIRI
      .label,
} as const;

const KeputusanPertubuhanNav: React.FC = () => {
  const permissionMap = Object.fromEntries(
    Object.entries(PERMISSIONS).map(([key, label]) => [
      key,
      AuthHelper.hasAuthority(label),
    ])
  ) as Record<keyof typeof PERMISSIONS, boolean>;

  const navigate = useNavigate();
  const location = useLocation();

  const visibleTabs = tabs.map((tab) => ({
    ...tab,
    hasPermission: !tab.permissionName || permissionMap[tab.permissionName],
  }));

  if (visibleTabs.length === 0) {
    return <ForbiddenPage internal />;
  }
  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          background: "#fff",
          borderRadius: "10px",
          p: 1,
          boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
          overflowX: "auto",
        }}
      >
        {visibleTabs.map((tab) => {
          const isActive = location.pathname.includes(tab.path);
          return (
            <Button
              key={tab.path}
              onClick={() => tab.hasPermission && navigate(tab.path)}
              disabled={!tab.hasPermission}
              sx={{
                textTransform: "none",
                fontWeight: isActive ? 600 : 400,
                color: !tab.hasPermission ? "#999" : isActive ? "#fff" : "#333",
                background: isActive ? "var(--primary-color)" : "transparent",
                borderRadius: "5px",
                px: 3,
                py: 1,
                transition: "all 0.3s ease",
                "&:hover": {
                  background: isActive
                    ? "var(--primary-color)"
                    : tab.hasPermission
                      ? "#F1F4FA"
                      : "transparent",
                },
              }}
            >
              {tab.label}
            </Button>
          );
        })}
      </Box>
      <Box>
        <Outlet />
      </Box>
    </Box>
  );
};

export default KeputusanPertubuhanNav;
