import { Box, Grid, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import {
  capitalizeWords,
  getMalaysiaAddressList,
  getStateNameById,
} from "../../../../helpers/utils";
import { ApplicationStatusEnum } from "../../../../helpers/enums";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { Controller, FieldValues, useForm } from "react-hook-form";
import DataTable, { IColumn } from "@/components/datatable";
import { CrudFilter } from "@refinedev/core";
import { useQuery } from "@/helpers";
import Input from "@/components/input/Input";
import ForbiddenPage from "@/pages/forbidden";
import { MaklumatTabProps } from "../maklumatSelectionTabs";

function PemegangJawatanTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const sectionStyle = {
      color: "var(--primary-color)",
      marginBottom: "30px",
      borderRadius: "16px",
      fontSize: "14px",
      fontWeight: "500 !important",
    };

    const [displaySenaraiAjk, setDisplaySenaraiAjk] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);

    const { control, setValue, watch, getValues, reset, handleSubmit } =
      useForm<FieldValues>({
        defaultValues: {
          pageNo: 1,
          pageSize: 10,
          positionType: "",
          state: "",
          societyStatus: "",
          searchQuery: "",
        },
      });

    const jenisPemegangJawatan = [
      { value: "Juruaudit", label: t("auditor") },
      { value: "Pemegang Amanah", label: t("trustee") },
      { value: "Pegawai Awam", label: t("civilServant") },
      { value: "Pegawai Harta", label: t("propertyOfficer") },
    ];

    const branchStatusList = Object.entries(ApplicationStatusEnum)
      .filter(([key, value]) => key === "001" || key === "008")
      .map(([key, value]) => ({
        value: key,
        label: t(value),
      }));

    const handleClearSearch = () => {
      reset();
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: 10 },
        { field: "pageNo", operator: "eq", value: 1 },
        {
          field: "positionType",
          operator: "eq",
          value: null,
        },
        {
          field: "state",
          operator: "eq",
          value: null,
        },
        {
          field: "societyStatus",
          operator: "eq",
          value: null,
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: null,
        },
      ];
      refetch({ filters });
    };

    const malaysiaAddressList = getMalaysiaAddressList() ?? [];
    const stateOptions = useMemo(() => {
      return malaysiaAddressList.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }, []);

    const handleSearch = () => {
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: getValues("pageSize") },
        { field: "pageNo", operator: "eq", value: getValues("pageNo") },
        {
          field: "positionType",
          operator: "eq",
          value: getValues("positionType"),
        },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "societyStatus",
          operator: "eq",
          value: getValues("societyStatus"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      refetch({ filters });
    };

    const handleChangePage = (newPage: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: watch("pageSize"), operator: "eq" },
        { field: "pageNo", value: newPage, operator: "eq" },
        {
          field: "positionType",
          operator: "eq",
          value: getValues("positionType"),
        },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "societyStatus",
          operator: "eq",
          value: getValues("societyStatus"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", newPage);
      refetch({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: newPageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
        {
          field: "positionType",
          operator: "eq",
          value: getValues("positionType"),
        },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "societyStatus",
          operator: "eq",
          value: getValues("societyStatus"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", 1);
      setValue("pageSize", newPageSize);
      refetch({ filters });
    };

    const columns: IColumn[] = [
      {
        field: "name",
        headerName: t("nameOfPositionHolder"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.name ?? "-"}</Box>;
        },
      },
      {
        field: "identificationNo",
        headerName: t("idNumberPlaceholder"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.identificationNo ?? "-"}</Box>;
        },
      },
      {
        field: "positionType",
        headerName: t("position"),
        align: "center",
        renderCell: ({ row }: any) => {
          const positionName = jenisPemegangJawatan.find(
            (item) => item.value === row?.positionType
          )?.label;
          return <Box>{positionName ?? "-"}</Box>;
        },
      },
      {
        field: "societyName",
        headerName: t("namaPertubuhan"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.societyName ?? "-"}</Box>;
        },
      },
      {
        field: "societyNo",
        headerName: t("noPPM"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.societyNo ?? "-"}</Box>;
        },
      },
      {
        field: "societyApplicationStatus",
        headerName: t("applicationStatusCode"),
        align: "center",
        renderCell: ({ row }: any) => {
          return t(
            ApplicationStatusEnum[
              (row?.societyApplicationStatus as keyof typeof ApplicationStatusEnum) ||
                "0"
            ]
          );
        },
      },
      {
        field: "societyStatus",
        headerName: t("organizationStatus"),
        align: "center",
        renderCell: ({ row }: any) => {
          const isActive = row?.societyStatus === "001";
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Typography
                className="status-pertubuhan-text"
                sx={{
                  backgroundColor: "#fff",
                  border: `2px solid ${
                    isActive ? "var(--success)" : "var(--error)"
                  }`,
                }}
              >
                {isActive ? t("active") : t("inactive")}
              </Typography>
            </Box>
          );
        },
      },
      {
        field: "societyStateCode",
        headerName: t("state"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{getStateNameById(row?.societyStateCode)}</Box>;
        },
      },
    ];

    const { data, isLoading, refetch } = useQuery({
      url: `society/admin/positionHolders/findAllByParam`,
      filters: [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
      ],
      autoFetch: false,
      onSuccess: (data) => {
        const AJKdata = data?.data?.data?.data || [];
        const total = data?.data?.data?.total;
        setTotal(total);
        setDisplaySenaraiAjk(AJKdata);
      },
    });

    useEffect(() => {
      const filters: CrudFilter[] = [
        {
          field: "pageSize",
          value: 10,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: 1,
          operator: "eq",
        },
      ];
      refetch({ filters });
    }, []);

    return (
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <form onSubmit={handleSubmit(handleSearch)}>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("Senarai Pertubuhan")}
            </Typography>
            <Controller
              name="positionType"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="select"
                  label={capitalizeWords(t("typeOfOfficer"))}
                  options={jenisPemegangJawatan}
                />
              )}
            />

            {/* state */}
            <Controller
              name="state"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="select"
                  label={t("state")}
                  options={stateOptions}
                />
              )}
            />

            {/* branch status */}
            <Controller
              name="societyStatus"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="select"
                  label={t("organizationStatus")}
                  options={branchStatusList}
                />
              )}
            />
            {/* branch application status */}
            {/* <Controller
              name="societyStatus"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="select"
                  label={t("statusCawangan")}
                  options={branchApplicationStatusList}
                />
              )}
            /> */}

            {/* finding/carian */}
            <Controller
              name="searchQuery"
              control={control}
              render={({ field }) => <Input {...field} label={t("search")} />}
            />
            <Grid container mt={3} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  // flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious onClick={handleClearSearch}>
                  {t("previous")}
                </ButtonPrevious>
                <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </form>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {total}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("rekodDijumpai")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("ajkList")}
            </Typography>

            <DataTable
              columns={columns}
              rows={displaySenaraiAjk}
              page={watch("pageNo")}
              rowsPerPage={watch("pageSize")}
              totalCount={total}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
              isLoading={isLoading} 
            />
          </Box>
        </Box>
      </Box>
    );
  }
}

export default PemegangJawatanTab;
