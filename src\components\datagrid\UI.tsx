import Box from "@mui/material/Box/Box";
import Typography from "@mui/material/Typography/Typography";
import {
  DataGrid as DefaultDataGrid,
  DataGridProps,
  GridValidRowModel,
  GridPagination,
} from "@mui/x-data-grid";
import { RefAttributes } from "react";
import { useTranslation } from "react-i18next";

export interface DataGridUIBaseProps<R extends GridValidRowModel = any>
  extends DataGridProps<R>,
    RefAttributes<HTMLDivElement> {
  noResultMessage?: string;
   /**
   * @default true
   */
  sortable?: boolean;
}

const DataGridPagination = (props: any) => {
  const { t } = useTranslation();
  return (
    <GridPagination
      rowsPerPageOptions={[5, 10, 25, 50]}
      labelRowsPerPage={t("rowsPerPage")}
      labelDisplayedRows={({ from, to, count }) =>
        t("paginationLabelDisplayedRows", {
          from,
          to,
          count,
        })
      }
      {...props}
    />
  );
};

export const DataGridUI = <
  R extends GridValidRowModel = any,
  PropType extends DataGridUIBaseProps<R> = DataGridUIBaseProps<R>
>({
  noResultMessage: initialNoResultMessage,
  disableColumnMenu = true,
  sortable = true,
  ...props
}: PropType) => {
  const { t } = useTranslation();

  const columns = props.columns.map((item) => ({
    ...item,
    ...(!sortable && { sortable: false }),
    headerAlign: item?.headerAlign ?? "center",
  }));
  const noResultMessage = initialNoResultMessage ?? t("noData");

  return (
    <DefaultDataGrid
      {...props}
      {...{ disableColumnMenu, columns }}
      sx={{
        border: "none",
        backgroundColor: "white",
        borderRadius: "10px",
        fontFamily: "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
        overflow: "auto",

        "& .MuiDataGrid-columnHeaderTitle": {
          backgroundColor: "white",
          borderTopLeftRadius: "10px",
          borderTopRightRadius: "10px",
          width: "auto",
          color: "#666 !important",
          fontSize: "13px !important",
        },
        ".MuiDataGrid-cell": {
          whiteSpace: "normal !important",
          wordWrap: "break-word !important",
          color: "#666666 !important",
          fontSize: "13px !important",
        },
        minHeight: "100px",
        opacity: props?.loading ? 0.5 : 1,
        ...props.sx,
      }}
      slots={{
        noRowsOverlay: () => (
          <Box sx={{ display: "flex", justifyContent: "center", pt: 3 }}>
            <Typography>{noResultMessage}</Typography>
          </Box>
        ),
        noResultsOverlay: () => (
          <Box sx={{ display: "flex", justifyContent: "center", pt: 3 }}>
            <Typography>{noResultMessage}</Typography>
          </Box>
        ),
        pagination: DataGridPagination,
        ...props?.slots,
      }}
      localeText={{
        footerRowSelected: (count) =>
          t("paginationFooterRowSelected", { count }),
        ...props.localeText,
      }}
      sortingMode="client"
    />
  );
};
