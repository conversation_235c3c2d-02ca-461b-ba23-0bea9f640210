import React, { useEffect, useState } from "react";
import {
  Box,
  Checkbox,
  CircularProgress,
  FormHelperText,
  Grid,
  SxProps,
  TextField,
  Theme,
  Typography,
} from "@mui/material";
import { TrainingQuestion } from "@/pages/internal-training/createStepThree";
import { useTranslation } from "react-i18next";

interface QuestionFragmentProps {
  no: number;
  headerStyle: SxProps<Theme>;
  labelStyle: SxProps<Theme>;
  borderStyle: SxProps<Theme>;
  data: TrainingQuestion;
  isTrainingLoading?: boolean;
  handleDataChange: (i: number, data: TrainingQuestion) => void;
  onErrorChange: (hasError: boolean) => void;
}

const QuestionFragment: React.FC<QuestionFragmentProps> = ({
  no,
  headerStyle,
  borderStyle,
  labelStyle,
  data,
  isTrainingLoading,
  handleDataChange,
  onErrorChange,
}) => {
  const { t } = useTranslation();
  const [answer, setAnswer] = useState(-1);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState<TrainingQuestion>({
    id: data.id,
    question: data.question,
    isUpdated: false,
    error: false,
    answers: [
      {
        id: "1",
        answer: "",
        correct: false,
        sequenceOrder: 1,
      },
      {
        id: "2",
        answer: "",
        correct: false,
        sequenceOrder: 2,
      },
      {
        id: "3",
        answer: "",
        correct: false,
        sequenceOrder: 3,
      },
      {
        id: "4",
        answer: "",
        correct: false,
        sequenceOrder: 4,
      },
    ],
  });

  // Helper to check validity
  const validateForm = (dataToCheck: TrainingQuestion) => {
    const errors: { [key: string]: string } = {};

    // Question cannot be empty
    if (!dataToCheck.question || dataToCheck.question.trim() === "") {
      errors.question = t("fieldRequired");
    }

    // All answers must be non-empty
    if (
      !dataToCheck.answers ||
      dataToCheck.answers.length === 0 ||
      dataToCheck.answers.some((a) => !a.answer || a.answer.trim() === "")
    ) {
      errors.answers = t("fieldRequired");
    }

    // At least one correct answer must be checked
    const hasCorrect = dataToCheck.answers.some((a) => a.correct);
    if (!hasCorrect) {
      errors.correct = t("fieldRequired");
    }

    return errors;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const updatedData = { ...formData, [name]: value, isUpdated: true };
    setFormData(updatedData);
    validation(true, updatedData);
  };

  const handleAnswerBox = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    i: number
  ) => {
    const newAnswers = formData.answers.map((ans, index) =>
      index === i ? { ...ans, answer: e.target.value } : ans
    );
    const updatedData = { ...formData, answers: newAnswers, isUpdated: true };
    setFormData(updatedData);
    validation(true, updatedData);
  };

  const handleCheckBox = (
    e: React.ChangeEvent<HTMLInputElement>,
    i: number
  ) => {
    const newAnswers = formData.answers.map((ans, index) => ({
      ...ans,
      correct: index === i ? e.target.checked : false,
    }));

    const updatedData = { ...formData, answers: newAnswers, isUpdated: true };
    setFormData(updatedData);
    validation(true, updatedData);
  };

  useEffect(() => {
    console.log("data", data.answers);
    if (
      Object.keys(data).length > 0 &&
      data.question &&
      data.answers &&
      data.answers.length > 0
    ) {
      //console.log("data", data);
      for (const a in data.answers) {
        if (data.answers[a].correct) {
          setAnswer(parseInt(a));
        }
      }
      setFormData((prevState) => ({
        ...prevState,
        id: data.id,
        question: data.question,
        isUpdated: data.isUpdated || false,
        error: data.error || false,
        answers: data.answers,
      }));
      //validation(false, "4");
    }
    if (Object.keys(data).length > 0) {
      const initialErrors = validateForm(data);
      onErrorChange(Object.keys(initialErrors).length > 0);
    }
  }, [data.id]);

  const validation = (updateMain: boolean, currentData: TrainingQuestion) => {
    const errors = validateForm(currentData);
    const hasError = Object.keys(errors).length > 0;

    setFormErrors(errors);
    setFormData((prev) => ({ ...prev, error: hasError }));

    // Notify parent immediately
    onErrorChange(hasError);

    if (updateMain) handleDataChange(no - 1, currentData);
  };

  return (
    <>
      {isTrainingLoading ? (
        <CircularProgress />
      ) : (
        <Box sx={borderStyle}>
          <Typography sx={headerStyle}>{t("quiz")}</Typography>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {`${t("question")} ${no}`}{" "}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size={"small"}
                fullWidth
                required
                name="question"
                value={formData.question}
                error={!!formErrors.question}
                helperText={formErrors.question}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {`${t("answer")}`} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Box sx={borderStyle}>
                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={1}>
                    <Typography sx={{ ...labelStyle, mt: 0, pt: 0 }}>
                      {`${t("bil")}`}
                    </Typography>
                  </Grid>
                  <Grid item xs={9}>
                    <Typography sx={{ ...labelStyle, mt: 0, pt: 0 }}>
                      {`${t("answer")}`}
                    </Typography>
                  </Grid>
                  <Grid item xs={2}>
                    <Typography sx={{ ...labelStyle, mt: 0, pt: 0 }}>
                      {`${t("correctAnswer")}`}
                    </Typography>
                  </Grid>
                  {formData.answers.map((el, i) => {
                    return (
                      <Grid key={i} container spacing={2} sx={{ mt: 0 }}>
                        <Grid item xs={1}>
                          <Typography
                            sx={{ ...labelStyle, pl: 3, lineHeight: 0.5 }}
                          >
                            {`${i + 1}`}
                          </Typography>
                        </Grid>
                        <Grid item xs={9}>
                          <TextField
                            size={"small"}
                            fullWidth
                            required
                            name={`answer${i}`}
                            value={el.answer}
                            onChange={(e) => handleAnswerBox(e, i)}
                          />
                        </Grid>
                        <Grid item xs={2}>
                          <Checkbox
                            checked={el.correct}
                            onChange={(e) => handleCheckBox(e, i)}
                            sx={{ pl: 3 }}
                          />
                        </Grid>
                      </Grid>
                    );
                  })}
                  {formErrors.correct ? (
                    <FormHelperText sx={{ color: "red", ml: 3 }}>
                      {t("fieldRequired")}
                    </FormHelperText>
                  ) : null}
                </Grid>
              </Box>
            </Grid>
          </Grid>
        </Box>
      )}
    </>
  );
};

export default QuestionFragment;
