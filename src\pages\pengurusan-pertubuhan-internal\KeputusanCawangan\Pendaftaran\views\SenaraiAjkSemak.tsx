import {
  <PERSON>,
  CircularProgress,
  <PERSON>rid,
  <PERSON><PERSON>ield,
  Typography,
} from "@mui/material";
import { t } from "i18next";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { allAjkListData, allBranchAjkListData } from "@/redux/ajkReducer";
import { CitizenshipStatus, ListGender } from "@/helpers/enums";
import { downloadPdfFromBase64, formatDate, useQuery } from "@/helpers";
import { getSocietyDataRedux } from "@/redux/societyDataReducer";
import { useBack, useCustom } from "@refinedev/core";
import { useAJK } from "@/helpers/hooks/useAJK";
import { ButtonOutline, ButtonPrimary } from "@/components";
import { API_URL } from "@/api";

const labelStyle = {
  fontWeight: "400!important",
  marginBottom: "8px",
  fontSize: "14px",
  color: "#666666",
};

function SenaraiAjkBranchSemak() {
  const ajkData = useSelector(allBranchAjkListData);

  const societyData = useSelector(getSocietyDataRedux);
  const { id, branchId } = useParams();

  const back = useBack();
  const { getDesignationName } = useAJK();

  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  const rowHeight = "80px";

  const { refetch: exportAjkRefetch } = useQuery({
    url: `society/committee/exportAjk`,
    autoFetch: false,
    onSuccess: (data) => {
      const link = data?.data;
      downloadPdfFromBase64(link, "AJK");
    },
  });

  const { refetch: exportAjkGeneralFetch } = useQuery({
    url: `society/committee/exportAjkGeneral`,
    autoFetch: false,
    onSuccess: (data) => {
      const link = data?.data;
      downloadPdfFromBase64(link, "AJK_UMUM");
    },
  });

  const exportUmum = () => {
    exportAjkGeneralFetch({
      filters: [
        {
          field: "societyId",
          value: branchList?.data?.data?.societyId,
          operator: "eq",
        },
        {
          field: "branchId",
          value: branchId,
          operator: "eq",
        },
      ],
    });
  };

  const exportAjk = () => {
    exportAjkRefetch({
      filters: [
        {
          field: "societyId",
          value: branchList?.data?.data?.societyId,
          operator: "eq",
        },
        {
          field: "branchId",
          value: branchId,
          operator: "eq",
        },
      ],
    });
  };

  const getGenderLabel = (val: string) => {
    if (!val) return "-";
    const genderItem = ListGender?.find((item) => item.value === val) ?? null;
    return genderItem?.label ? t(genderItem.label) : "-";
  };

  const getCitizenshipStatus = (val: string) => {
    if (!val) return "-";
    const citizenshipItem =
      CitizenshipStatus?.find((item) => item.value === parseInt(val)) ?? null;
    return citizenshipItem?.label ? t(citizenshipItem.label) : "-";
  };

  if (isLoadingBranch) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        sx={{ minHeight: "100px" }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            px: 2,
            py: 4,
            display: "grid",
            gap: 1,
          }}
        >
          <Typography sx={{ fontWeight: "400!important" }}>
            {branchList?.data?.data?.name}
          </Typography>
          <Typography sx={{ fontWeight: "400!important" }}>
            {branchList?.data?.data?.branchApplicationNo ?? "-"}
          </Typography>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            pl: 2,
            p: 3,
            mt: 1,
            borderRadius: "10px",
            border: "0.5px solid #dfdfdf",
          }}
        >
          <Box
            sx={{
              mb: 3,
            }}
          >
            <Typography color={"primary"} sx={{ fontWeight: "500!important" }}>
              {t("viewAjk")}
            </Typography>
          </Box>
          <Box>
            <Grid container spacing={2} sx={{ pb: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography sx={labelStyle}>{t("organizationName")}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  value={societyData?.societyName ?? "-"}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography sx={labelStyle}>
                  {t("organizationNumber2")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  value={
                    societyData?.societyNo ?? societyData?.applicationNo ?? "-"
                  }
                />
              </Grid>
            </Grid>
          </Box>
        </Box>

        <Box
          sx={{
            pl: 2,
            p: 3,
            mt: 1,
            borderRadius: "10px",
            border: "0.5px solid #dfdfdf",
          }}
        >
          <Box
            sx={{
              mb: 3,
            }}
          >
            <Typography color={"primary"} sx={{ fontWeight: "500!important" }}>
              {t("viewAjk")}
            </Typography>
          </Box>
          <Box
            sx={{
              pl: 2,
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box sx={{ display: "flex", width: "100%", overflow: "scroll" }}>
              {/* Labels */}
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  textAlign: "center",
                  borderRight: "1px solid #DADADA",
                  flexShrink: 0,
                }}
              >
                {[
                  { label: t("jawatan"), key: "designationCode" },
                  { label: t("namaPenuh"), key: "fullName" },
                  { label: t("gender"), key: "gender" },
                  { label: t("idNumberPlaceholder"), key: "idNumber" },
                  { label: t("citizen"), key: "citizen" },
                  { label: t("dateAndBirthPlace"), key: "dobAndPob" },
                  { label: t("phone"), key: "phone" },
                  { label: t("pekerjaan"), key: "occupation" },
                  { label: t("residentialAddress"), key: "residentialAddress" },
                  { label: t("employerAddress"), key: "employerAddress" },
                ].map((row) => (
                  <Box
                    key={row.key}
                    sx={{
                      ...labelStyle,
                      fontWeight: "500!important",
                      fontSize: "14px",
                      color: "#666666",
                      minHeight: rowHeight,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: "0 20px",
                    }}
                  >
                    {row.label}
                  </Box>
                ))}
              </Box>

              {/* Data Columns */}
              <Box sx={{ display: "flex" }}>
                {ajkData?.length > 0 &&
                  ajkData.map((item: any, colIndex: number) => {
                    const {
                      id,
                      committeeName,
                      gender,
                      identificationNo,
                      citizenshipStatus,
                      dateOfBirth,
                      phoneNumber,
                      jobCode,
                      committeeAddress,
                      committeeEmployerAddress,
                    } = item;

                    const rowData = [
                      getDesignationName(item)?.toUpperCase(),
                      committeeName ?? "-",
                      getGenderLabel(gender),
                      identificationNo ?? "-",
                      getCitizenshipStatus(citizenshipStatus),
                      formatDate(dateOfBirth),
                      phoneNumber ?? "-",
                      jobCode ?? "-",
                      committeeAddress ?? "-",
                      committeeEmployerAddress ?? "-",
                    ];

                    return (
                      <Box
                        key={id}
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          textAlign: "center",
                          borderRight:
                            colIndex === ajkData.length - 1
                              ? "none"
                              : "1px solid #DADADA",
                          flex: 1,
                          minWidth: 0,
                        }}
                      >
                        {rowData.map((value, index) => (
                          <Box
                            key={index}
                            sx={{
                              ...labelStyle,
                              padding: "0 30px",
                              minHeight: rowHeight,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              whiteSpace: index >= 8 ? "pre-wrap" : "nowrap",
                              wordBreak: "break-word",
                              color:
                                index === 0
                                  ? "var(--primary-color)"
                                  : "inherit",
                              width: "100%",
                              maxWidth: "100%",
                            }}
                          >
                            {value || ""}
                          </Box>
                        ))}
                      </Box>
                    );
                  })}
              </Box>
            </Box>
          </Box>
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 1 }}
          >
            {ajkData?.length > 0 ? (
              <>
                <ButtonPrimary onClick={() => exportAjk()}>
                  {t("printAJK")}
                </ButtonPrimary>
                <ButtonPrimary onClick={() => exportUmum()}>
                  {t("printGeneral")}
                </ButtonPrimary>
              </>
            ) : null}
            <ButtonOutline onClick={back}>{t("back")}</ButtonOutline>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

export default SenaraiAjkBranchSemak;
