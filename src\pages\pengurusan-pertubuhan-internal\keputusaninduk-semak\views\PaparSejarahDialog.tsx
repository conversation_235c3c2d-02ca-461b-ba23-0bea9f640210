import {
  Box,
  CircularProgress,
  Dialog,
  DialogContent, 
  Typography,
} from "@mui/material"; 
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { useTranslation } from "react-i18next";
import { ApplicationStatusList, getSectionByCode } from "@/helpers";

type PaparSejarahProps = {
  isOpen: boolean;
  isLoading: boolean;
  dataList: any[];
  listType: number | null;
  isOpenHandler: (isOpen: boolean) => void;
};

const PaparSejarahDialog = ({
  isOpen = false,
  isLoading = false,
  dataList,
  listType,
  isOpenHandler,
}: PaparSejarahProps) => {
  const { t } = useTranslation();
  const statusMap = Object.fromEntries(
    ApplicationStatusList.map((item) => [item.id, item.value])
  );
  return (
    <Dialog
      open={isOpen}
      onClose={() => isOpenHandler(false)}
      PaperProps={{
        style: {
          borderRadius: "8px",
        },
      }}
      slotProps={{
        backdrop: {
          style: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            backdropFilter: "blur(4px)",
          },
        },
      }}
    >
      <DialogContent sx={{ py: 4 }}>
        <Box
          sx={{
            p: 3,
            mt: 1,
            borderRadius: "10px",
            border: "0.5px solid #dfdfdf",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              pb: 4,
              color: "var(--primary-color)",
            }}
            gap={2}
          >
            <ChevronLeftIcon
              sx={{ cursor: "pointer" }}
              onClick={() => isOpenHandler(!isOpen)}
            />
            <Typography sx={{ fontWeight: "4001important" }}>
              {listType === 1 ? t("historyCancellation") : t("historyAmendments")}
            </Typography>
          </Box> 
          {/* @ts-ignore */}
          {dataList.length && listType === 1 ? (
            // @ts-ignore
            dataList?.map((item, index) => (
              <Box sx={{ display: "flex", mb: 4 }} key={item.id}>
                <Box sx={{ mr: 2 }}>
                  {/* @ts-ignore */}
                  {index !== dataList?.length - 1 && !item?.finished && (
                    <Box
                      sx={{
                        width: 2,
                        height: "100%",
                        backgroundColor: "#DADADA",
                        ml: 2,
                      }}
                    />
                  )}
                </Box>

                <Box sx={{ width: "100%" }}>
                  <Box sx={{ display: "flex", gap: 3 }}>
                    <Typography
                      sx={{
                        fontWeight: "300",
                        border: "1px solid #5088FF",
                        background: "#5088FF80",
                        borderRadius: "9px",
                        color: "#fff",
                        px: 2,
                        py: 1,
                        fontSize: "12px",
                      }}
                    >
                      #{index + 1}
                    </Typography>
                    <Typography
                      sx={{
                        fontWeight: "300",
                        border: "1px solid #DADADA",
                        background: "transparent",
                        borderRadius: "9px",
                        color: "#666666",
                        px: 2,
                        py: 1,
                        fontSize: "12px",
                      }}
                    >
                      {item?.cancelledDate}
                    </Typography>
                    <Typography
                      sx={{
                        fontWeight: "300",
                        border: "1px solid #DADADA",
                        background: "transparent",
                        borderRadius: "9px",
                        color: "#666666",
                        px: 2,
                        py: 1,
                        fontSize: "12px",
                      }}
                    >
                      {getSectionByCode(item?.section)?.description || ""}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography
                      sx={{
                        fontWeight: "300",
                        background: "transparent",
                        borderRadius: "9px",
                        color: "#666666",
                        py: 1,
                        fontSize: "12px",
                      }}
                    >
                      Pegawai: {item?.roName}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      p: 3,
                      border: "1px solid #D9D9D9",
                      borderRadius: "14px",
                      width: "100%",
                      minHeight: "150px",
                      position: "relative",
                    }}
                  >
                    <Typography sx={{ mb: 3, color: "#666666" }}>
                      {item?.reason}
                    </Typography>
                    {/* <Box
                      sx={{
                        fontFamily: '"Poppins", sans-serif',
                        backgroundColor: item.finished
                          ? "var(--primary-color)"
                          : "#FF000080",
                        border: `1px solid ${
                          item.finished ? "var(--primary-color)" : "#FF0000"
                        }`,
                        padding: "6px 20px",
                        borderRadius: "18px",
                        color: "#fff",
                        fontSize: "14px",
                        fontWeight: "400",
                        position: "absolute",
                        bottom: "20px",
                        right: "20px",
                      }}
                    >
                      {item.finished ? t("completed") : t("belumselesai")}
                    </Box> */}
                  </Box>
                </Box>
              </Box>
            ))
          ) : dataList.length && listType === 2 ? ( // @ts-ignore
            dataList?.map((item, index) => (
              <Box sx={{ display: "flex", mb: 4 }} key={item.id}>
                <Box sx={{ mr: 2 }}>
                  {/* @ts-ignore */}
                  {index !== dataList?.length - 1 && !item?.finished && (
                    <Box
                      sx={{
                        width: 2,
                        height: "100%",
                        backgroundColor: "#DADADA",
                        ml: 2,
                      }}
                    />
                  )}
                </Box>

                <Box sx={{ width: "100%" }}>
                  <Box sx={{ display: "flex", gap: 3 }}>
                    <Typography
                      sx={{
                        fontWeight: "300",
                        border: "1px solid #5088FF",
                        background: "#5088FF80",
                        borderRadius: "9px",
                        color: "#fff",
                        px: 2,
                        py: 1,
                        fontSize: "12px",
                      }}
                    >
                      #{index + 1}
                    </Typography>
                    <Typography
                      sx={{
                        fontWeight: "300",
                        border: "1px solid #DADADA",
                        background: "transparent",
                        borderRadius: "9px",
                        color: "#666666",
                        px: 2,
                        py: 1,
                        fontSize: "12px",
                      }}
                    >
                      {item?.approvedDate}
                    </Typography>
                    <Typography
                      sx={{
                        fontWeight: "300",
                        border: "1px solid #DADADA",
                        background: "transparent",
                        borderRadius: "9px",
                        color: "#666666",
                        px: 2,
                        py: 1,
                        fontSize: "12px",
                      }}
                    >
                      {statusMap[item.applicationStatusCode] || ""}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography
                      sx={{
                        fontWeight: "300",
                        background: "transparent",
                        borderRadius: "9px",
                        color: "#666666",
                        py: 1,
                        fontSize: "12px",
                      }}
                    >
                      Pegawai: {item?.roName}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      p: 3,
                      border: "1px solid #D9D9D9",
                      borderRadius: "14px",
                      width: "100%",
                      minHeight: "150px",
                      position: "relative",
                    }}
                  >
                    <Typography sx={{ mb: 3, color: "#666666" }}>
                      {item?.amendmentGoal}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))
          ) : (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                minHeight: "300px",
              }}
            >
              {isLoading ? (
                <CircularProgress />
              ) : (
                <Typography className="label">{t("noData")}</Typography>
              )}
            </Box>
          )}
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default PaparSejarahDialog;
